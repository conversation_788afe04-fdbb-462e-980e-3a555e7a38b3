"""
Unified Persona Node for LangGraph-based Datagenius System.

This module provides a unified agent node that handles all persona types through
configuration-driven behavior strategies, eliminating code duplication while
maintaining persona-specific capabilities.
"""

import logging
import asyncio
from typing import Dict, Any, Optional, List, Type, Union
from datetime import datetime
from abc import ABC, abstractmethod

from ..states.unified_state import UnifiedDatageniusState, WorkflowStatus
from .base_agent_node import BaseAgentNode
from ..tools.mcp_integration import MCPToolManager
from ..intelligence.business_context_integration import BusinessContextManager
from ..intelligence.cross_agent_intelligence import CrossAgentIntelligenceManager
from ..strategies.extensible_strategy_system import (
    ExtensiblePersonaStrategy,
    extensible_strategy_registry
)
from ..config.dynamic_config_loader import dynamic_config_loader

logger = logging.getLogger(__name__)


class PersonaStrategy(ABC):
    """
    Abstract base class for persona-specific behavior strategies.

    This allows different personas to have specialized behavior while
    sharing common infrastructure and eliminating code duplication.
    All behavior is configuration-driven to maintain extensibility.
    """

    def __init__(self, config: Dict[str, Any]):
        """Initialize the persona strategy."""
        self.config = config
        self.logger = logging.getLogger(f"{__name__}.{self.__class__.__name__}")

        # Extract configuration dynamically - no hardcoded values
        self.capabilities = config.get("capabilities", [])
        self.supported_intents = config.get("supported_intents", [])
        self.tools = config.get("tools", [])
        self.methodology_framework = config.get("methodology_framework")
        self.strategy_config = config.get("strategy_config", {})
        self.processing_rules = config.get("processing_rules", {})
        self.prompt_templates = config.get("prompt_templates", {})
        
    @abstractmethod
    async def process_message(
        self, 
        state: UnifiedDatageniusState,
        context: Dict[str, Any]
    ) -> UnifiedDatageniusState:
        """
        Process a message using persona-specific logic.
        
        Args:
            state: Current workflow state
            context: Additional context for processing
            
        Returns:
            Updated workflow state
        """
        pass
    
    @abstractmethod
    def get_system_prompt(self, context: Dict[str, Any]) -> str:
        """
        Get persona-specific system prompt.
        
        Args:
            context: Context for prompt generation
            
        Returns:
            System prompt string
        """
        pass
    
    @abstractmethod
    def get_specialized_tools(self) -> List[str]:
        """
        Get list of specialized tools for this persona.
        
        Returns:
            List of tool names
        """
        pass
    
    def can_handle_intent(self, intent: str) -> bool:
        """
        Check if this persona can handle the given intent.
        
        Args:
            intent: Intent to check
            
        Returns:
            True if persona can handle the intent
        """
        return intent in self.supported_intents
    
    def get_capability_score(self, request_context: Dict[str, Any]) -> float:
        """
        Calculate capability score for handling a request.
        
        Args:
            request_context: Context of the request
            
        Returns:
            Score between 0.0 and 1.0 indicating capability
        """
        # Default implementation based on intent matching
        intent = request_context.get("detected_intent", "")
        if self.can_handle_intent(intent):
            return 0.8
        
        # Check for keyword matches in capabilities
        message = request_context.get("message", "").lower()
        capability_matches = sum(1 for cap in self.capabilities if cap.lower() in message)
        
        if capability_matches > 0:
            return min(0.6 + (capability_matches * 0.1), 0.9)
        
        return 0.1  # Minimal capability for fallback


class AnalysisPersonaStrategy(PersonaStrategy):
    """Strategy for data analysis personas."""
    
    async def process_message(
        self, 
        state: UnifiedDatageniusState,
        context: Dict[str, Any]
    ) -> UnifiedDatageniusState:
        """Process message using analysis-specific logic."""
        self.logger.info("Processing message with analysis persona strategy")
        
        # Extract analysis-specific context
        analysis_request = context.get("analysis_request")
        data_context = context.get("data_context", {})
        
        # Apply methodology framework if configured
        if self.methodology_framework == "UNDERSTAND_ASSESS_EXECUTE_DELIVER":
            return await self._apply_uaed_framework(state, context)
        
        # Default analysis processing
        return await self._process_analysis_request(state, analysis_request, data_context)
    
    def get_system_prompt(self, context: Dict[str, Any]) -> str:
        """Get analysis-specific system prompt."""
        business_profile = context.get("business_profile", {})
        industry = business_profile.get("industry", "general")
        
        base_prompt = """You are an expert data analyst specializing in extracting insights from data.
        
Your capabilities include:
- Statistical analysis and data exploration
- Data visualization and chart creation
- Trend analysis and pattern recognition
- Report generation and insight extraction
- Data cleaning and preprocessing

"""
        
        if industry != "general":
            base_prompt += f"You have specialized knowledge in the {industry} industry. "
        
        if self.methodology_framework:
            base_prompt += f"\nFollow the {self.methodology_framework} methodology for structured analysis."
        
        return base_prompt
    
    def get_specialized_tools(self) -> List[str]:
        """Get analysis-specific tools."""
        return [
            "data_analyzer",
            "chart_generator", 
            "report_generator",
            "pandasai_query",
            "statistical_analyzer",
            "trend_detector",
            "data_cleaner",
            "visualization_engine"
        ]
    
    async def _apply_uaed_framework(
        self, 
        state: UnifiedDatageniusState,
        context: Dict[str, Any]
    ) -> UnifiedDatageniusState:
        """Apply Understand-Assess-Execute-Deliver framework."""
        current_stage = state.workflow_context.get("uaed_stage", "understand")
        
        if current_stage == "understand":
            # Understand the data and requirements
            state.workflow_context["uaed_stage"] = "assess"
            state.workflow_context["understanding_complete"] = True
            
        elif current_stage == "assess":
            # Assess data quality and analysis approach
            state.workflow_context["uaed_stage"] = "execute"
            state.workflow_context["assessment_complete"] = True
            
        elif current_stage == "execute":
            # Execute the analysis
            state.workflow_context["uaed_stage"] = "deliver"
            state.workflow_context["execution_complete"] = True
            
        elif current_stage == "deliver":
            # Deliver results and insights
            state.workflow_context["delivery_complete"] = True
            state.workflow_status = WorkflowStatus.COMPLETED
        
        return state
    
    async def _process_analysis_request(
        self,
        state: UnifiedDatageniusState,
        analysis_request: Optional[Dict[str, Any]],
        data_context: Dict[str, Any]
    ) -> UnifiedDatageniusState:
        """Process standard analysis request."""
        if analysis_request:
            # Set up analysis context
            state.workflow_context["analysis_type"] = analysis_request.get("type", "general")
            state.workflow_context["data_sources"] = data_context.get("sources", [])
            state.workflow_context["analysis_ready"] = True
        
        return state


class MarketingPersonaStrategy(PersonaStrategy):
    """Strategy for marketing personas."""
    
    async def process_message(
        self, 
        state: UnifiedDatageniusState,
        context: Dict[str, Any]
    ) -> UnifiedDatageniusState:
        """Process message using marketing-specific logic."""
        self.logger.info("Processing message with marketing persona strategy")
        
        # Extract marketing-specific context
        marketing_task = context.get("marketing_task")
        brand_context = context.get("brand_context", {})
        
        # Process marketing request
        return await self._process_marketing_request(state, marketing_task, brand_context)
    
    def get_system_prompt(self, context: Dict[str, Any]) -> str:
        """Get marketing-specific system prompt."""
        business_profile = context.get("business_profile", {})
        brand_voice = business_profile.get("brand_voice", "professional")
        target_audience = business_profile.get("target_audience", "general")
        
        base_prompt = f"""You are an expert marketing strategist and content creator.
        
Your capabilities include:
- Content creation and copywriting
- Campaign strategy and planning
- Social media management
- Brand development and positioning
- Audience analysis and targeting
- Performance tracking and optimization

Brand Voice: {brand_voice}
Target Audience: {target_audience}

"""
        
        return base_prompt
    
    def get_specialized_tools(self) -> List[str]:
        """Get marketing-specific tools."""
        return [
            "content_generator",
            "social_media_poster",
            "campaign_analyzer",
            "brand_strategy_generator",
            "audience_analyzer",
            "performance_tracker",
            "competitor_analyzer",
            "email_marketing_generator"
        ]
    
    async def _process_marketing_request(
        self,
        state: UnifiedDatageniusState,
        marketing_task: Optional[Dict[str, Any]],
        brand_context: Dict[str, Any]
    ) -> UnifiedDatageniusState:
        """Process marketing request."""
        if marketing_task:
            # Set up marketing context
            state.workflow_context["marketing_type"] = marketing_task.get("type", "general")
            state.workflow_context["brand_guidelines"] = brand_context.get("guidelines", {})
            state.workflow_context["marketing_ready"] = True
        
        return state


class ConciergePersonaStrategy(PersonaStrategy):
    """Strategy for concierge personas."""

    async def process_message(
        self,
        state: UnifiedDatageniusState,
        context: Dict[str, Any]
    ) -> UnifiedDatageniusState:
        """Process message using concierge-specific logic."""
        self.logger.info("Processing message with concierge persona strategy")

        # Extract concierge-specific context
        persona_request = context.get("persona_recommendation_request")
        user_intent = context.get("user_intent", {})

        # Process concierge request
        return await self._process_concierge_request(state, persona_request, user_intent)

    def get_system_prompt(self, context: Dict[str, Any]) -> str:
        """Get concierge-specific system prompt."""
        available_personas = context.get("available_personas", [])

        base_prompt = """You are a helpful AI concierge for the Datagenius platform.

Your capabilities include:
- Persona recommendation and guidance
- Intent analysis and understanding
- Conversation management and coordination
- Data attachment assistance
- Workflow coordination and routing
- User guidance and support

"""

        if available_personas:
            persona_list = ", ".join([p.get("name", p.get("id", "")) for p in available_personas])
            base_prompt += f"Available personas: {persona_list}\n"

        return base_prompt

    def get_specialized_tools(self) -> List[str]:
        """Get concierge-specific tools."""
        return [
            "persona_recommender",
            "data_attachment_assistant",
            "context_manager",
            "conversation_state_manager",
            "intent_analyzer",
            "workflow_coordinator"
        ]

    async def _process_concierge_request(
        self,
        state: UnifiedDatageniusState,
        persona_request: Optional[Dict[str, Any]],
        user_intent: Dict[str, Any]
    ) -> UnifiedDatageniusState:
        """Process concierge request and generate response."""
        import uuid
        from datetime import datetime
        from ..states.unified_state import add_message, MessageType

        # Get current message content
        current_message = state.get("current_message", {})
        user_message = current_message.get("content", "")

        # Generate appropriate response based on message content
        if not user_message.strip():
            response_content = "Hello! I'm the Datagenius Concierge. How can I help you today? I can assist you with finding the right AI persona for your task, answer questions, or help with your data."
        else:
            # Analyze the user's message and provide helpful response
            if any(keyword in user_message.lower() for keyword in ["hello", "hi", "hey", "greeting"]):
                response_content = "Hello! I'm the Datagenius Concierge. I'm here to help you navigate our AI personas and assist with your tasks. What would you like to work on today?"
            elif any(keyword in user_message.lower() for keyword in ["help", "assist", "guide"]):
                response_content = "I'd be happy to help! I can assist you with:\n\n• Finding the right AI persona for your specific task\n• Answering questions about Datagenius capabilities\n• Helping you prepare and attach data\n• Guiding you through workflows\n\nWhat specific assistance do you need?"
            elif any(keyword in user_message.lower() for keyword in ["persona", "agent", "ai"]):
                response_content = "I can help you find the perfect AI persona for your needs! We have several specialized personas:\n\n• **Analysis Persona** - For data analysis, visualization, and insights\n• **Marketing Persona** - For content creation and marketing strategies\n• **Classification Persona** - For organizing and categorizing content\n\nWhat type of task are you looking to accomplish?"
            elif any(keyword in user_message.lower() for keyword in ["data", "analysis", "analyze"]):
                response_content = "Great! For data analysis tasks, I recommend our Analysis Persona. It can help you:\n\n• Analyze datasets and generate insights\n• Create visualizations and charts\n• Perform statistical analysis\n• Generate reports\n\nWould you like me to connect you with the Analysis Persona, or do you have questions about preparing your data first?"
            elif any(keyword in user_message.lower() for keyword in ["marketing", "content", "campaign"]):
                response_content = "Perfect! For marketing tasks, our Marketing Persona is ideal. It can help you:\n\n• Create compelling marketing content\n• Develop campaign strategies\n• Write copy and messaging\n• Analyze market trends\n\nWould you like me to connect you with the Marketing Persona?"
            else:
                # General helpful response
                response_content = f"I understand you're asking about: {user_message[:100]}{'...' if len(user_message) > 100 else ''}\n\nI'm here to help guide you to the right solution. Could you tell me more about what you're trying to accomplish? This will help me recommend the best AI persona or provide the most relevant assistance."

        # Set up persona recommendation context if needed
        if persona_request:
            state.workflow_context["recommendation_type"] = persona_request.get("type", "general")
            state.workflow_context["user_preferences"] = user_intent.get("preferences", {})
            state.workflow_context["concierge_ready"] = True

        # Create response message
        response_message = {
            "id": str(uuid.uuid4()),
            "content": response_content,
            "type": MessageType.AGENT.value,
            "timestamp": datetime.now().isoformat(),
            "agent_id": "concierge-agent",
            "metadata": {
                "persona_type": "concierge",
                "processing_type": "concierge_assistance",
                "user_intent_detected": bool(user_intent),
                "persona_request_detected": bool(persona_request)
            },
            "in_response_to": current_message.get("id")
        }

        # Add response message to state
        state = add_message(state, response_message, MessageType.AGENT)

        return state


class ClassificationPersonaStrategy(PersonaStrategy):
    """Strategy for classification personas."""

    async def process_message(
        self,
        state: UnifiedDatageniusState,
        context: Dict[str, Any]
    ) -> UnifiedDatageniusState:
        """Process message using classification-specific logic."""
        self.logger.info("Processing message with classification persona strategy")

        # Extract classification-specific context
        classification_request = context.get("classification_request")
        data_context = context.get("data_context", {})

        # Process classification request
        return await self._process_classification_request(state, classification_request, data_context)

    def get_system_prompt(self, context: Dict[str, Any]) -> str:
        """Get classification-specific system prompt."""
        classification_types = context.get("classification_types", ["text", "data"])

        base_prompt = """You are an expert classification specialist.

Your capabilities include:
- Text classification and categorization
- Data organization and labeling
- Content sorting and structuring
- Sentiment analysis and emotion detection
- Entity extraction and recognition
- Topic modeling and analysis

"""

        if classification_types:
            types_list = ", ".join(classification_types)
            base_prompt += f"Specialized in: {types_list} classification\n"

        return base_prompt

    def get_specialized_tools(self) -> List[str]:
        """Get classification-specific tools."""
        return [
            "text_classifier",
            "data_categorizer",
            "sentiment_analyzer",
            "entity_extractor",
            "topic_modeler",
            "document_classifier",
            "content_organizer"
        ]

    async def _process_classification_request(
        self,
        state: UnifiedDatageniusState,
        classification_request: Optional[Dict[str, Any]],
        data_context: Dict[str, Any]
    ) -> UnifiedDatageniusState:
        """Process classification request."""
        if classification_request:
            # Set up classification context
            state.workflow_context["classification_type"] = classification_request.get("type", "general")
            state.workflow_context["data_sources"] = data_context.get("sources", [])
            state.workflow_context["classification_ready"] = True

        return state


class DefaultPersonaStrategy(PersonaStrategy):
    """Default strategy for unknown or generic personas."""

    async def process_message(
        self,
        state: UnifiedDatageniusState,
        context: Dict[str, Any]
    ) -> UnifiedDatageniusState:
        """Process message using default logic."""
        self.logger.info("Processing message with default persona strategy")

        # Basic processing for unknown persona types
        state.workflow_context["default_processing"] = True
        state.workflow_context["persona_type"] = "default"

        return state

    def get_system_prompt(self, context: Dict[str, Any]) -> str:
        """Get default system prompt."""
        return """You are a helpful AI assistant capable of handling various tasks.

Your capabilities include:
- General conversation and assistance
- Basic analysis and information processing
- Task coordination and workflow management
- User guidance and support

"""

    def get_specialized_tools(self) -> List[str]:
        """Get default tools."""
        return [
            "conversation_handler",
            "basic_analyzer",
            "workflow_coordinator"
        ]


class UnifiedPersonaNode(BaseAgentNode):
    """
    Unified agent node that handles all persona types through configuration.

    This node eliminates code duplication by using extensible strategy patterns for
    persona-specific behavior while maintaining shared infrastructure.
    All behavior is configuration-driven with no hardcoded values.
    """

    def __init__(
        self,
        persona_config: Dict[str, Any],
        business_profile: Optional[Dict[str, Any]] = None
    ):
        """
        Initialize the unified persona node.

        Args:
            persona_config: Configuration for the persona
            business_profile: Optional business profile context
        """
        super().__init__(
            agent_id=persona_config.get("persona_id", "unknown"),
            agent_type=persona_config.get("agent_type", "default"),
            config=persona_config
        )

        # Create persona-specific strategy using extensible system
        self.persona_strategy = self._create_persona_strategy(persona_config)

        # Initialize shared infrastructure
        self.tool_manager = MCPToolManager()
        self.business_context = BusinessContextManager(business_profile)
        self.cross_agent_intelligence = CrossAgentIntelligenceManager()

        # Configuration
        self.persona_config = persona_config
        self.business_profile = business_profile or {}

        # Performance tracking
        self.processing_metrics = {
            "messages_processed": 0,
            "tools_executed": 0,
            "errors_encountered": 0,
            "average_processing_time": 0.0
        }

        self.logger.info(f"Initialized UnifiedPersonaNode for {self.agent_id} ({self.agent_type})")

    def _create_persona_strategy(self, config: Dict[str, Any]) -> ExtensiblePersonaStrategy:
        """
        Create persona-specific behavior strategy using extensible system.

        Args:
            config: Persona configuration

        Returns:
            ExtensiblePersonaStrategy instance
        """
        # Get strategy ID from configuration
        strategy_id = config.get("strategy_id") or config.get("agent_type", "default")

        # Load strategy from extensible registry
        return extensible_strategy_registry.get_strategy(strategy_id, config)

    async def __call__(self, state: UnifiedDatageniusState) -> UnifiedDatageniusState:
        """
        Process the current state using the unified persona node.

        Args:
            state: Current workflow state

        Returns:
            Updated workflow state
        """
        start_time = datetime.now()

        try:
            self.logger.info(f"Processing message with {self.agent_type} persona")

            # Prepare processing context
            context = await self._prepare_processing_context(state)

            # Apply business profile context
            context = await self.business_context.enrich_context(context)

            # Apply cross-agent intelligence
            context = await self.cross_agent_intelligence.apply_intelligence(context, state)

            # Process with persona-specific strategy
            updated_state = await self.persona_strategy.process_message(state, context)

            # Execute tools if needed
            if self._should_execute_tools(updated_state, context):
                updated_state = await self._execute_tools(updated_state, context)

            # Update metrics
            self._update_metrics(start_time)

            # Update state with processing metadata
            updated_state.agent_context[self.agent_id] = {
                "last_processed": datetime.now().isoformat(),
                "persona_type": self.agent_type,
                "processing_successful": True
            }

            return updated_state

        except Exception as e:
            self.logger.error(f"Error processing message: {e}")
            self.processing_metrics["errors_encountered"] += 1

            # Return state with error information
            state.agent_context[self.agent_id] = {
                "last_processed": datetime.now().isoformat(),
                "persona_type": self.agent_type,
                "processing_successful": False,
                "error": str(e)
            }

            return state

    async def _prepare_processing_context(self, state: UnifiedDatageniusState) -> Dict[str, Any]:
        """
        Prepare context for processing.

        Args:
            state: Current workflow state

        Returns:
            Processing context dictionary
        """
        context = {
            "message": state.messages[-1].content if state.messages else "",
            "conversation_history": [msg.content for msg in state.messages[-5:]],
            "workflow_context": state.workflow_context,
            "agent_context": state.agent_context,
            "business_profile": self.business_profile,
            "persona_config": self.persona_config,
            "available_tools": self.persona_strategy.get_specialized_tools()
        }

        # Add persona-specific context extraction
        if self.agent_type == "analysis":
            context["analysis_request"] = state.workflow_context.get("analysis_request")
            context["data_context"] = state.workflow_context.get("data_context", {})
        elif self.agent_type == "marketing":
            context["marketing_task"] = state.workflow_context.get("marketing_task")
            context["brand_context"] = state.workflow_context.get("brand_context", {})
        elif self.agent_type == "concierge":
            context["persona_recommendation_request"] = state.workflow_context.get("persona_recommendation_request")
            context["user_intent"] = state.workflow_context.get("user_intent", {})
        elif self.agent_type == "classification":
            context["classification_request"] = state.workflow_context.get("classification_request")
            context["data_context"] = state.workflow_context.get("data_context", {})

        return context

    def _should_execute_tools(self, state: UnifiedDatageniusState, context: Dict[str, Any]) -> bool:
        """
        Determine if tools should be executed based on state and context.

        Args:
            state: Current workflow state
            context: Processing context

        Returns:
            True if tools should be executed
        """
        # Check for tool execution indicators
        tool_indicators = [
            "analysis_ready",
            "marketing_ready",
            "concierge_ready",
            "classification_ready",
            "tool_execution_requested"
        ]

        return any(state.workflow_context.get(indicator, False) for indicator in tool_indicators)

    async def _execute_tools(self, state: UnifiedDatageniusState, context: Dict[str, Any]) -> UnifiedDatageniusState:
        """
        Execute tools based on the current context.

        Args:
            state: Current workflow state
            context: Processing context

        Returns:
            Updated workflow state
        """
        try:
            # Get specialized tools for this persona
            available_tools = self.persona_strategy.get_specialized_tools()

            # Execute tools through the tool manager
            tool_results = await self.tool_manager.execute_tools(
                tools=available_tools,
                context=context,
                state=state
            )

            # Update state with tool results
            state.workflow_context["tool_results"] = tool_results
            state.workflow_context["tools_executed"] = True

            self.processing_metrics["tools_executed"] += 1

            return state

        except Exception as e:
            self.logger.error(f"Error executing tools: {e}")
            state.workflow_context["tool_execution_error"] = str(e)
            return state

    def _update_metrics(self, start_time: datetime) -> None:
        """
        Update processing metrics.

        Args:
            start_time: Processing start time
        """
        processing_time = (datetime.now() - start_time).total_seconds()

        self.processing_metrics["messages_processed"] += 1

        # Update average processing time
        current_avg = self.processing_metrics["average_processing_time"]
        message_count = self.processing_metrics["messages_processed"]

        new_avg = ((current_avg * (message_count - 1)) + processing_time) / message_count
        self.processing_metrics["average_processing_time"] = new_avg

    def get_capabilities(self) -> List[str]:
        """
        Get capabilities of this persona node.

        Returns:
            List of capabilities
        """
        return self.persona_strategy.capabilities

    def get_supported_intents(self) -> List[str]:
        """
        Get supported intents for this persona.

        Returns:
            List of supported intents
        """
        return self.persona_strategy.supported_intents

    def get_specialized_tools(self) -> List[str]:
        """
        Get specialized tools for this persona.

        Returns:
            List of tool names
        """
        return self.persona_strategy.get_specialized_tools()

    def can_handle_intent(self, intent: str) -> bool:
        """
        Check if this persona can handle the given intent.

        Args:
            intent: Intent to check

        Returns:
            True if persona can handle the intent
        """
        return self.persona_strategy.can_handle_intent(intent)

    def get_capability_score(self, request_context: Dict[str, Any]) -> float:
        """
        Calculate capability score for handling a request.

        Args:
            request_context: Context of the request

        Returns:
            Score between 0.0 and 1.0 indicating capability
        """
        return self.persona_strategy.get_capability_score(request_context)

    def get_system_prompt(self, context: Optional[Dict[str, Any]] = None) -> str:
        """
        Get system prompt for this persona.

        Args:
            context: Optional context for prompt generation

        Returns:
            System prompt string
        """
        prompt_context = context or {}
        prompt_context["business_profile"] = self.business_profile

        return self.persona_strategy.get_system_prompt(prompt_context)

    def get_processing_metrics(self) -> Dict[str, Any]:
        """
        Get processing metrics for this persona node.

        Returns:
            Dictionary of metrics
        """
        return self.processing_metrics.copy()

    async def reset_metrics(self) -> None:
        """Reset processing metrics."""
        self.processing_metrics = {
            "messages_processed": 0,
            "tools_executed": 0,
            "errors_encountered": 0,
            "average_processing_time": 0.0
        }

        self.logger.info(f"Reset metrics for {self.agent_id}")


# Factory functions for creating unified persona nodes
async def create_unified_persona_node_from_config(
    persona_id: str,
    config_override: Optional[Dict[str, Any]] = None,
    business_profile: Optional[Dict[str, Any]] = None
) -> Optional[UnifiedPersonaNode]:
    """
    Factory function for creating unified persona nodes from dynamic configuration.

    Args:
        persona_id: Persona identifier to load configuration for
        config_override: Optional configuration override
        business_profile: Optional business profile context

    Returns:
        Configured UnifiedPersonaNode instance or None if config not found
    """
    try:
        # Load configuration dynamically
        persona_config = await dynamic_config_loader.load_config(persona_id)

        if not persona_config:
            logger.warning(f"No configuration found for persona: {persona_id}")
            return None

        # Apply configuration override
        if config_override:
            persona_config.update(config_override)

        # Ensure required fields
        persona_config.setdefault("persona_id", persona_id)
        persona_config.setdefault("agent_type", "default")

        return UnifiedPersonaNode(persona_config, business_profile)

    except Exception as e:
        logger.error(f"Error creating unified persona node for {persona_id}: {e}")
        return None


def create_unified_persona_node(
    persona_config: Dict[str, Any],
    business_profile: Optional[Dict[str, Any]] = None
) -> UnifiedPersonaNode:
    """
    Factory function for creating unified persona nodes from provided configuration.

    Args:
        persona_config: Configuration for the persona
        business_profile: Optional business profile context

    Returns:
        Configured UnifiedPersonaNode instance
    """
    return UnifiedPersonaNode(persona_config, business_profile)


async def create_persona_from_template(
    template_id: str,
    persona_id: str,
    customizations: Optional[Dict[str, Any]] = None,
    business_profile: Optional[Dict[str, Any]] = None
) -> Optional[UnifiedPersonaNode]:
    """
    Create a persona node from a template with customizations.

    Args:
        template_id: Template identifier to use as base
        persona_id: New persona identifier
        customizations: Custom configuration overrides
        business_profile: Optional business profile context

    Returns:
        Configured UnifiedPersonaNode instance or None if template not found
    """
    try:
        # Load template configuration
        template_config = await dynamic_config_loader.load_config(template_id)

        if not template_config:
            logger.warning(f"No template found: {template_id}")
            return None

        # Create new configuration from template
        persona_config = template_config.copy()
        persona_config["persona_id"] = persona_id
        persona_config["template_id"] = template_id

        # Apply customizations
        if customizations:
            persona_config.update(customizations)

        return UnifiedPersonaNode(persona_config, business_profile)

    except Exception as e:
        logger.error(f"Error creating persona from template {template_id}: {e}")
        return None
